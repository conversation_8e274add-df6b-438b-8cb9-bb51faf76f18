import {toSentenceCase} from '../../utils'

const ICONS = [
  'caretDown',
  'caretLeft',
  'caretRight',
  'caretUp',
  'arrowLeft',
  'arrowRight',
  'amazonMusic',
  'bookmark',
  'bookmarkFilled',
  'bullets',
  'calendar',
  'checkmark',
  'close',
  'copy',
  'document',
  'download',
  'facebook',
  'hamburger',
  'instagram',
  'magnifyingGlass',
  'pandora',
  'pinterest',
  'play',
  'jumpAhead',
  'jumpBack',
  'audioNext',
  'audioBack',
  'closedCaptioning',
  'playCircle',
  'podcasts',
  'printer',
  'profile',
  'spotify',
  'star',
  'starHollow',
  'tuneIn',
  'twitter',
  'wordmark',
  'youtube',
  'share',
  'basket',
  'pause',
]

export default {
  name: 'button',
  title: 'Button',
  type: 'object',
  preview: {
    select: {
      title: 'link.label',
    },
    prepare({title}: {title: string}) {
      return {
        title: `Button: ${title}`,
      }
    },
  },
  fields: [
    {
      name: 'link',
      title: 'Link',
      type: 'linkNoDisabled',
      validation: (Rule: any) => Rule.required(),
      options: {
        collapsible: false,
        collapsed: false,
      },
    },
    {
      name: 'style',
      title: 'Button Style',
      type: 'string',
      initialValue: 'secondary',
      validation: (Rule: any) => Rule.required(),
      options: {
        layout: 'radio',
        list: [
          {title: 'Primary', value: 'primary'},
          {title: 'Secondary', value: 'secondary'},
          {title: 'Rounded', value: 'rounded'},
          {title: 'Rounded Filled', value: 'roundedFilled'},
          {title: 'Bare', value: 'bare'},
        ],
      },
    },
    {
      name: 'labelSize',
      title: 'Label Size',
      type: 'string',
      initialValue: 'md',
      options: {
        list: [
          {title: 'SM', value: 'sm'},
          {title: 'MD', value: 'md'},
          {title: 'LG', value: 'lg'},
        ],
        layout: 'radio',
      },
    },
    {
      name: 'icon',
      title: 'Icon',
      type: 'string',
      description: 'Optional icon name',
      options: {
        list: ICONS.map((icon) => ({title: toSentenceCase(icon), value: icon})),
      },
    },
    {
      name: 'iconPosition',
      title: 'Icon Position',
      type: 'string',
      initialValue: 'right',
      hidden: ({parent}: {parent: any}) => !parent?.icon,
      options: {
        layout: 'radio',
        list: [
          {title: 'Left', value: 'left'},
          {title: 'Right', value: 'right'},
        ],
      },
    },
  ],
}
