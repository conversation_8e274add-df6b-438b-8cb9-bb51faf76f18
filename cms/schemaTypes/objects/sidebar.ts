export default {
  name: 'sidebar',
  title: 'Sidebar',
  type: 'object',
  preview: {
    select: {
      items: 'items',
    },
    prepare({items}: {items: any}) {
      return {title: `Sidebar: ${items?.length || 0} items`}
    },
  },
  fields: [
    {
      name: 'items',
      title: 'Items',
      type: 'array',

      of: [
        {
          name: 'titleAndText',
          title: 'Title and Text',
          type: 'object',
          preview: {
            select: {
              title: 'title',
              subtitle: 'text',
            },
          },
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'text',
              title: 'Text',
              type: 'string',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'link',
              title: 'Link',
              type: 'linkNoLabel',
              initialValue: {
                linkType: 'disabled',
              },
            },
          ],
        },
        {
          name: 'heading',
          title: 'Heading',
          type: 'object',
          preview: {
            select: {
              title: 'title',
            },
            prepare({title}: {title: string}) {
              return {
                title: `Heading: ${title}`,
              }
            },
          },
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
            },
            {
              name: 'titleSize',
              title: 'Title Size',
              type: 'string',
              initialValue: 'md',
              options: {
                layout: 'radio',
                list: [
                  {title: 'Small', value: 'sm'},
                  {title: 'Medium', value: 'md'},
                ],
              },
            },
            {
              name: 'link',
              title: 'Link',
              type: 'linkNoLabel',
              initialValue: {
                linkType: 'disabled',
              },
            },
            {
              name: 'hasLine',
              title: 'Has Line',
              type: 'boolean',
              initialValue: true,
            },
          ],
        },
        {
          type: 'cardItem',
        },
        {
          type: 'cardItems',
        },
        {
          type: 'button',
        },
        {
          name: 'textAndImage',
          title: 'Text and Image',
          type: 'object',
          preview: {
            select: {
              title: 'title',
              image: 'image',
            },
            prepare({title, image}: {title: string; image: any}) {
              return {
                title: `Text and Image: ${title}`,
                media: image,
              }
            },
          },
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
            },
            {
              name: 'link',
              title: 'Link',
              type: 'linkNoLabel',
              initialValue: {
                linkType: 'disabled',
              },
            },
            {
              name: 'image',
              title: 'Image',
              type: 'imageAsset',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'desktopImageWidth',
              title: 'Desktop Image Width',
              type: 'number',
              description: 'Defaults to 240px if not specified',
            },
            {
              name: 'mobileImageWidth',
              title: 'Mobile Image Width',
              type: 'number',
              description: 'Defaults to 150px if not specified',
            },
          ],
        },
      ],
    },
  ],
}
