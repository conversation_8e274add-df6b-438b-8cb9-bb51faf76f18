import getRichTextFields from '../../utils/richText'

export default {
  name: 'eyebrowAndTitleSection',
  title: 'Eyebrow And Title Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'eyebrow',
      title: 'Eyebrow',
      type: 'string',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      title: 'Description',
      name: 'description',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong']})],
    },
  ],
  preview: {
    select: {
      title: 'title',
      subtitle: 'eyebrow',
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {title, subtitle, cmsSettings} = selection
      const isHidden = cmsSettings?.isHidden

      return {
        title: title || 'Eyebrow and Title Section',
        subtitle: `${isHidden ? '⚪' : '🟢'} ${subtitle || 'No eyebrow'}`,
      }
    },
  },
}
