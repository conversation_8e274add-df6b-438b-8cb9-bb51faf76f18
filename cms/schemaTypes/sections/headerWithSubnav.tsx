import {Rule} from 'sanity'
import getRichTextFields from '../../utils/richText'

export default {
  name: 'headerWithSubnav',
  title: 'Header With Subnav',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'hasBreadcrumb',
      title: 'Has Breadcrumb',
      type: 'boolean',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'title',
      title: 'Title',
      type: 'array',
      validation: (Rule: Rule) => Rule.required(),
      of: [
        getRichTextFields({
          items: [],
        }),
        {
          type: 'subnavTitleImage',
        },
      ],
    },
    {
      name: 'links',
      title: 'Links',
      type: 'array',
      of: [{type: 'link'}],
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Header with Subnav'
      const isHidden = cmsSettings?.isHidden

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} Header with Subnav`,
      }
    },
  },
}
