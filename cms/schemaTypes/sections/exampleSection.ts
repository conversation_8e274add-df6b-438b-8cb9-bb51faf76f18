import {Rule} from 'sanity'
import getRichTextFields from '../../utils/richText'
import {requiredImageDimensions} from '../../utils/imageAssetUtilities'

export default {
  name: 'exampleSection',
  title: 'Example Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'sidebar',
      title: 'Sidebar',
      type: 'sidebar',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      title: 'Description',
      name: 'description',
      type: 'array',
      validation: (Rule: any) => Rule.required(),
      of: [getRichTextFields({items: ['bullet', 'heading2', 'link', 'strong']})],
    },
    {
      title: 'Items',
      name: 'items',
      type: 'array',
      validation: (Rule: any) => Rule.required().min(3).max(3),
      of: [
        {
          type: 'object',
          fields: [
            {
              title: 'Title',
              name: 'title',
              type: 'string',
              validation: (Rule: any) => Rule.required(),
            },
            {
              title: 'Description',
              name: 'description',
              type: 'text',
              rows: 3,
              validation: (Rule: any) => Rule.required(),
            },
            {
              type: 'imageAsset',
              name: 'image',
              title: 'Image',
              validation: (Rule: Rule) => requiredImageDimensions({Rule, required: true}),
            },
            {
              type: 'linkNoLabel',
              name: 'link',
              title: 'Link',
            },
          ],
        },
      ],
    },
    {
      title: 'Video',
      name: 'video',
      type: 'video',
      validation: (Rule: any) => Rule.required(),
    },
    {
      title: 'Referenced Items',
      name: 'referencedItems',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [
            {type: 'recipe'},
            {type: 'article'},
            {type: 'radioEpisode'},
            {type: 'class'},
            {type: 'tvEpisode'},
            // {type: 'product'},
          ],
        },
      ],
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Example Section'
      const isHidden = cmsSettings?.isHidden

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} Example Section`,
      }
    },
  },
}
