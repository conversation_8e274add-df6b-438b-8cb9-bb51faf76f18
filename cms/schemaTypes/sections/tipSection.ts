export default {
  name: 'tipSection',
  title: 'TipSection',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'content',
      title: 'Content',
      type: 'tip',
      validation: (Rule: any) => Rule.required(),
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      console.log('selection', selection)
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Tip Section'
      const isHidden = cmsSettings?.isHidden

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} Tip Section`,
      }
    },
  },
}
