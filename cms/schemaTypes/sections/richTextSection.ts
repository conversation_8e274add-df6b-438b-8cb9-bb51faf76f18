import {Rule} from 'sanity'
import getRichTextFields from '../../utils/richText'

export default {
  name: 'richTextSection',
  title: 'Rich Text Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      title: 'Content',
      name: 'content',
      type: 'array',
      validation: (Rule: Rule) => Rule.required(),
      of: [
        getRichTextFields({
          items: ['bullet', 'heading2', 'heading3', 'link', 'strong'],
        }),
        {
          type: 'tip',
        },
        {
          type: 'button',
        },
      ],
    },
    {
      name: 'textAlignment',
      title: 'Text Alignment',
      type: 'string',
      options: {
        list: [
          {title: 'Left', value: 'left'},
          {title: 'Center', value: 'center'},
        ],
        layout: 'radio',
      },
      initialValue: 'left',
      validation: (Rule: Rule) => Rule.required(),
      hidden: ({parent}: {parent: any}) => parent?.hasSidebar,
    },
    {
      name: 'contentWidth',
      title: 'Content Width',
      type: 'string',
      options: {
        list: [
          {title: 'Full', value: 'full'},
          {title: 'Medium', value: 'md'},
        ],
        layout: 'radio',
      },
      initialValue: 'full',
      validation: (Rule: Rule) => Rule.required(),
      hidden: ({parent}: {parent: any}) => parent?.hasSidebar,
    },
    {
      name: 'hasSidebar',
      title: 'Has Sidebar',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'sidebarSide',
      title: 'Sidebar Side',
      type: 'string',
      options: {
        list: [
          {title: 'Left', value: 'left'},
          {title: 'Right', value: 'right'},
        ],
        layout: 'radio',
      },
      initialValue: 'left',
      hidden: ({parent}: {parent: any}) => !parent?.hasSidebar,
    },
    {
      name: 'sidebarContent',
      title: 'Sidebar Content',
      type: 'array',
      of: [{type: 'sidebar'}],
      hidden: ({parent}: {parent: any}) => !parent?.hasSidebar,
      validation: (Rule: Rule) =>
        Rule.custom((value: any, context: any) => {
          if (context.parent?.hasSidebar && (!value || value.length === 0)) {
            return 'Sidebar content is required when Has Sidebar is enabled'
          }
          return true
        }),
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
      hasSidebar: 'hasSidebar',
      sidebarSide: 'sidebarSide',
    },
    prepare(selection: any) {
      const {cmsSettings, hasSidebar, sidebarSide} = selection
      const title = cmsSettings?.cmsTitle || 'Rich Text Section'
      const isHidden = cmsSettings?.isHidden
      const subtitle = hasSidebar ? `With ${sidebarSide || 'left'} sidebar` : 'No sidebar'

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} ${subtitle}`,
      }
    },
  },
}
