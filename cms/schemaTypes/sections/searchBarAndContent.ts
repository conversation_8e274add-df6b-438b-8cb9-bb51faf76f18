import {Rule} from 'sanity'
import {toSentenceCase} from '../../utils'
import {referenceableCardTypes} from '../../utils/referenceableCardTypes'
import cardStyle from '../objects/cardStyle'

export default {
  name: 'searchBarAndContent',
  title: 'Search Bar and Content',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      description: 'Optional',
    },
    {
      name: 'emptyInitially',
      title: 'Empty Initially',
      type: 'boolean',
      description: 'If true, the search bar will be empty when the page loads.',
    },
    {
      name: 'searchFieldLabel',
      title: 'Search Field Label',
      type: 'string',
      description:
        'Optional. Default text that shows in the search field ie ("Search all recipes").',
    },
    {
      name: 'typesToQuery',
      title: 'Types to Query',
      type: 'array',
      of: [{type: 'string'}],
      validation: (Rule: Rule) =>
        Rule.custom((value: string[]) => {
          if (!value || value?.length === 0) {
            return 'Please select at least one content type to query'
          }
          return true
        }),
      options: {
        list: referenceableCardTypes.map((type) => ({
          title: toSentenceCase(type),
          value: type,
        })),
      },
    },
    {
      name: 'searchBarCategories',
      title: 'Search Bar Categories',
      type: 'array',
      description: 'Optional. Select up to 3 categories to display in the search bar.',
      of: [
        {
          type: 'reference',
          to: [{type: 'category'}],
        },
      ],
      validation: (Rule: Rule) => Rule.max(3),
    },
    {
      name: 'filterCategories',
      title: 'Filter Categories',
      description: 'Optional. Select up to 10 categories to display in the filter.',
      validation: (Rule: Rule) => Rule.max(10),
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'category'}],
        },
      ],
    },
    {
      name: 'cardStyle',
      title: 'Card Style',
      type: 'cardStyle',
      options: {
        collapsible: true,
        collapsed: false,
      },
    },
    {
      name: 'displayedCardFields',
      title: 'Displayed Card Fields',
      type: 'displayedCardFields',
      options: {
        collapsible: true,
        collapsed: false,
      },
    },
    {
      name: 'disableUrlUpdates',
      title: 'Disable URL Updates',
      initialValue: false,
      type: 'boolean',
      description:
        'If true, the search bar will not update the URL when the user selects a filter.',
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Search Bar and Content'
      const isHidden = cmsSettings?.isHidden

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} Search Bar and Content`,
      }
    },
  },
}
