/* INJECT_OBJECT_DEFINITION */
import searchBarAndContent from './searchBarAndContent'
import adSection from './adSection'
import subscriptionPlansSection from './subscriptionPlansSection'
import showAllPageSectionExamples from './showAllPageSectionExamples'
import tipSection from './tipSection'
import imageCarouselAndText from './imageCarouselAndText'
import logoSliderSection from './logoSliderSection'
import textAndImageSection from './textAndImageSection'
import headerWithSubnav from './headerWithSubnav'
import multiColumnContent from './multiColumnContent'
import contentList from './contentList'
import exampleSection from './exampleSection'
import richTextSection from './richTextSection'
import classCalendar from './classCalendar'
import imageAndRichTextWithButton from './imageAndRichTextWithButton'
import textAndStaticImageSection from './textAndStaticImageSection'
import textAndPointsWithImageSection from './textAndPointsWithImageSection'
import bigImageSection from './bigImageSection'
import threeColumnContentSection from './threeColumnContentSection'
import titleTextAndCtaSection from './titleTextAndCtaSection'
import eyebrowAndTitleSection from './eyebrowAndTitleSection'
import textCarouselWithLogos from './textCarouselWithLogos'
import lineLinkSection from './lineLinkSection'
import nextPreviousSection from './nextPreviousSection'

export const sectionTypes = [
  /* INJECT_OBJECT_NAME */
  searchBarAndContent,
  subscriptionPlansSection,
  showAllPageSectionExamples,
  tipSection,
  imageCarouselAndText,
  logoSliderSection,
  textAndImageSection,
  textAndStaticImageSection,
  headerWithSubnav,
  multiColumnContent,
  contentList,
  exampleSection,
  richTextSection,
  classCalendar,
  imageAndRichTextWithButton,
  textAndPointsWithImageSection,
  bigImageSection,
  threeColumnContentSection,
  titleTextAndCtaSection,
  eyebrowAndTitleSection,
  textCarouselWithLogos,
  nextPreviousSection,
  adSection,
  lineLinkSection,
]

export default sectionTypes
