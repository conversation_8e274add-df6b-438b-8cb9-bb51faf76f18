export default {
  name: 'multiColumnContent',
  title: 'Multi Column Content',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
    },
    {
      name: 'titleLink',
      title: 'Title Link',
      type: 'linkNoLabel',
      initialValue: {
        linkType: 'disabled',
      },
      options: {
        collapsible: true,
        collapsed: true,
      },
    },
    {
      name: 'layoutType',
      title: 'Layout Type',
      type: 'string',
      options: {
        list: [
          {title: '50-50', value: '50-50'},
          {title: '3-4-3', value: '3-4-3'},
          {title: '8-4', value: '8-4'},
        ],
      },
      initialValue: '50-50',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'columns',
      title: 'Columns',
      type: 'array',
      validation: (Rule: any) =>
        Rule.custom((columns: any[], context: any) => {
          const layoutType = context.parent?.layoutType || '50-50'
          let requiredCount = null
          if (layoutType === '3-4-3') {
            requiredCount = 3
          } else if (layoutType === '8-4') {
            requiredCount = 2
          } else if (layoutType === '50-50') {
            requiredCount = 2
          }

          if (!columns || columns.length !== requiredCount) {
            return `Must have exactly ${requiredCount} columns for ${layoutType} layout`
          }
          return true
        }),
      of: [
        {
          type: 'object',
          preview: {
            select: {
              items: 'items',
            },
            prepare({items}: {items: any[]}) {
              return {
                title: `${items?.length} item${items.length === 1 ? '' : 's'}` || 'Untitled',
              }
            },
          },
          fields: [
            {
              type: 'array',
              name: 'items',
              title: 'Item(s)',
              of: [
                {
                  type: 'object',
                  name: 'title',
                  title: 'Title',
                  fields: [
                    {
                      name: 'title',
                      title: 'Title',
                      type: 'string',
                    },
                    {
                      name: 'titleLink',
                      title: 'Title Link',
                      type: 'linkNoLabel',
                      initialValue: {
                        linkType: 'disabled',
                      },
                      options: {
                        collapsible: true,
                        collapsed: true,
                      },
                    },
                  ],
                },
                {
                  type: 'cardItem',
                },
                {
                  type: 'cardItems',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Multi Column Content'
      const isHidden = cmsSettings?.isHidden

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} Multi Column Content`,
      }
    },
  },
}
