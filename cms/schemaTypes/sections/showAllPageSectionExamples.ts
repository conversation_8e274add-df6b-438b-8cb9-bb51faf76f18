export default {
  name: 'showAllPageSectionExamples',
  title: 'Show All Page Section Examples',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Page Sections'
      const isHidden = cmsSettings?.isHidden

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} Page Sections`,
      }
    },
  },
}
