import getRichTextFields from '../../utils/richText'

export default {
  name: 'textAndStaticImageSection',
  title: 'Text and Static Image Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
    },
    {
      name: 'description',
      title: 'Description',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong']})],
    },
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'imageCaption',
      title: 'Image Caption',
      type: 'string',
    },
    {
      name: 'imageSide',
      title: 'Image Side',
      type: 'string',
      options: {
        list: [
          {title: 'Left', value: 'left'},
          {title: 'Right', value: 'right'},
        ],
        layout: 'radio',
      },
      initialValue: 'right',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'columns',
      title: 'Column Layout',
      type: 'string',
      options: {
        list: [
          {title: '50/50', value: '50-50'},
          {title: '5/7', value: '5-7'},
        ],
        layout: 'radio',
      },
      initialValue: '50-50',
      validation: (Rule: any) => Rule.required(),
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
      title: 'title',
      image: 'image',
    },
    prepare(selection: any) {
      const {cmsSettings, title, image} = selection
      const sectionTitle = cmsSettings?.cmsTitle || title
      const isHidden = cmsSettings?.isHidden

      return {
        title: sectionTitle,
        subtitle: `${isHidden ? '⚪' : '🟢'} ${title}`,
        media: image,
      }
    },
  },
}
