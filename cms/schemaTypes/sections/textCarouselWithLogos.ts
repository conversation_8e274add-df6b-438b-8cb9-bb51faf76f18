import {Rule} from 'sanity'

export default {
  name: 'textCarouselWithLogos',
  title: 'Text Carousel With Logos',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'carouselItems',
      title: 'Carousel Items',
      type: 'array',
      validation: (Rule: Rule) => Rule.required().min(1),
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              validation: (Rule: Rule) => Rule.required(),
            },
            {
              name: 'subtitle',
              title: 'Subtitle',
              type: 'string',
            },
          ],
        },
      ],
    },
    {
      name: 'logoItems',
      title: 'Logo Items',
      type: 'array',
      validation: (Rule: Rule) => Rule.required().min(1),
      of: [
        {
          type: 'object',
          preview: {
            select: {
              media: 'image',
            },
          },
          fields: [
            {
              name: 'image',
              title: 'Image',
              type: 'imageAsset',
              validation: (Rule: Rule) => Rule.required(),
            },
            {
              name: 'desktopWidth',
              title: 'Desktop Width',
              description: 'Width of the logo on desktop, defaults to 100px',
              type: 'number',
            },
            {
              name: 'mobileWidth',
              title: 'Mobile Width',
              description: 'Width of the logo on desktop, defaults to 100px',
              type: 'number',
            },
          ],
        },
      ],
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Text Carousel With Logos'
      const isHidden = cmsSettings?.isHidden

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} Text Carousel With Logos`,
      }
    },
  },
}
