export default {
  name: 'nextPreviousSection',
  title: 'Next Previous Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'labelType',
      title: 'Label Type',
      description:
        'Optional. Specify the type of content (e.g., "Recipe", "Story") to display in Next/Previous labels',
      type: 'string',
    },
    {
      name: 'items',
      title: 'Items',
      description: 'Add up to 2 items: first item will be "Previous", second will be "Next"',
      type: 'array',
      of: [
        {
          type: 'cardItem',
        },
      ],
      validation: (Rule: any) => Rule.max(2),
    },
  ],
  preview: {
    select: {
      items: 'items',
      labelType: 'labelType',
      cmsSettings: 'cmsSettings',
    },
    prepare({
      items,
      labelType,
      cmsSettings,
    }: {
      items: any[]
      labelType?: string
      cmsSettings?: any
    }) {
      const isHidden = cmsSettings?.isHidden
      return {
        title: 'Next Previous Section',
        subtitle: `${isHidden ? '⚪' : '🟢'} ${items?.length || 0} items${labelType ? ` • Type: ${labelType}` : ''}`,
      }
    },
  },
}
