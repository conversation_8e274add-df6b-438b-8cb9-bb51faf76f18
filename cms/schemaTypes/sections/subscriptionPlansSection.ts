import {Rule} from 'sanity'

export default {
  name: 'subscriptionPlansSection',
  title: 'Subscription Plans Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'subtitle',
      title: 'Subtitle',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'items',
      title: 'Subscription Plans',
      type: 'array',
      of: [{type: 'subscriptionPlanCard'}],
      validation: (Rule: Rule) => Rule.required().min(3).max(3),
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
      title: 'title',
    },
    prepare(selection: any) {
      const {cmsSettings, title} = selection
      const isHidden = cmsSettings?.isHidden

      return {
        title: title || 'Subscription Plans Section',
        subtitle: `${isHidden ? '⚪' : '🟢'} Subscription Plans Section`,
      }
    },
  },
}
