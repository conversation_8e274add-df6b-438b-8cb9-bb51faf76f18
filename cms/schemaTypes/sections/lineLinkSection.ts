export default {
  name: 'lineLinkSection',
  title: 'Line Link Section',
  type: 'object',
  options: {
    collapsible: false,
    collapsed: false,
  },
  fields: [
    {
      name: 'link',
      title: 'Link',
      type: 'link',
      options: {
        collapsible: false,
        collapsed: false,
      },
      validation: (Rule: any) => Rule.required(),
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Line Link Section'
      const isHidden = cmsSettings?.isHidden

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} Line Link Section`,
      }
    },
  },
}
