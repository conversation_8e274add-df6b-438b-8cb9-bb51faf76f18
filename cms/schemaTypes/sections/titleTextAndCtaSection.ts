import {defineType} from 'sanity'

export default defineType({
  name: 'titleTextAndCtaSection',
  title: 'Title, Text and CTA Section',
  type: 'object',
  fields: [
    {
      name: 'cmsSettings',
      type: 'cmsSettings',
    },
    {
      name: 'content',
      title: 'Content',
      type: 'titleTextAndCta',
      validation: (Rule) => Rule.required(),
    },
  ],
  preview: {
    select: {
      title: 'content.title',
      subtitle: 'content.description',
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {title, subtitle, cmsSettings} = selection
      const isHidden = cmsSettings?.isHidden

      return {
        title: title || 'Title, Text and CTA Section',
        subtitle: `${isHidden ? '⚪' : '🟢'} ${subtitle || 'No description'}`,
      }
    },
  },
})
