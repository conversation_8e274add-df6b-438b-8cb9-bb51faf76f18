export default {
  name: 'imageCarouselAndText',
  title: 'Image Carousel and Text',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'useContentReference',
      title: 'Use Content Reference',
      description:
        'If a reference is used, the content will be pulled from the reference. Any fields filled in after the reference will override the reference content.',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'referenceForContent',
      title: 'Reference for Content',
      type: 'reference',
      to: [{type: 'recipe'}],
      hidden: ({parent}: {parent: any}) => parent?.useContentReference === false,
      validation: (Rule: any) =>
        Rule.custom((field: any, context: any) => {
          if (context.parent?.useContentReference === true && !field) {
            return 'This is required when using a content reference'
          }
          return true
        }),
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) =>
        Rule.custom((field: any, context: any) => {
          if (!context.parent?.useContentReference && !field) {
            return 'This field is required'
          }
          return true
        }),
    },
    {
      name: 'authors',
      title: 'Authors',
      type: 'array',
      of: [{type: 'reference', to: [{type: 'person'}]}],
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      validation: (Rule: any) =>
        Rule.custom((field: any, context: any) => {
          if (!context.parent?.useContentReference && !field) {
            return 'This field is required'
          }
          return true
        }),
    },
    {
      name: 'images',
      title: 'Images',
      description: 'Note: This will be cropped to 4:3.',
      type: 'array',
      of: [{type: 'imageAssetNoAlt'}],
      validation: (Rule: any) =>
        Rule.custom((field: any, context: any) => {
          if (!context.parent?.useContentReference && !field) {
            return 'This field is required'
          }
          return true
        }),
    },
    {
      name: 'link',
      title: 'Link',
      type: 'link',
      initialValue: {
        linkType: 'disabled',
      },
      hidden: ({parent}: {parent: any}) => parent?.useContentReference,
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Image Carousel and Text'
      const isHidden = cmsSettings?.isHidden

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} Image Carousel and Text`,
      }
    },
  },
}
