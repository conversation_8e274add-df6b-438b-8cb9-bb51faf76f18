import { createClient } from 'next-sanity'
import { sendCraftRequest } from './craftCms'

export const SANITY_CONFIG = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION,
  useCdn: false,
}

export const getSanityClient = () => {
  const tokenizedClient = createClient({
    ...SANITY_CONFIG,
    token: process.env.SANITY_MIGRATION_TOKEN,
  })

  return tokenizedClient
}

const SANITY_ID_PREFIX_BY_TYPE = {
  category: 'category',
  subcategory: 'subcategory',
  recipe: 'recipe',
  person: 'person',
  image: 'image',
  radioEpisode: 'radioEpisode',
  tvEpisode: 'tvEpisode',
  magazineIssue: 'magazineIssue',
  article: 'article',
}

export const getSanityIdFromCraftId = (craftId: string, type: string) => {
  if (!SANITY_ID_PREFIX_BY_TYPE[type as keyof typeof SANITY_ID_PREFIX_BY_TYPE] || !craftId) {
    throw new Error(`Invalid type or craftId for getSanityIdFromCraftId. type: ${type}, craftId: ${craftId}`)
  }

  return `craft_${SANITY_ID_PREFIX_BY_TYPE[type as keyof typeof SANITY_ID_PREFIX_BY_TYPE]}_${craftId}`
}

export const sanityIdToCraftId = (id: string) => {
  return id.split('_')[2]
}

export const checkIfIdExistsInSanity = async (id: string | string[], type: string) => {
  if (!SANITY_ID_PREFIX_BY_TYPE[type as keyof typeof SANITY_ID_PREFIX_BY_TYPE]) {
    throw new Error(`Invalid type or craftId for getSanityIdFromCraftId. type: ${type}`)
  }

  if (!id) {
    throw new Error('Id is required in checkIfIdExists')
    return
  }

  const client = getSanityClient()
  const ids = Array.isArray(id) ? id : [id]
  const sanityIds = ids.map(id => getSanityIdFromCraftId(id, type))

  // Use GROQ to fetch all documents in a single query
  const query = '*[_id in $ids]'
  const results = await client.fetch(query, { ids: sanityIds })

  // If single ID was passed, return single result
  if (!Array.isArray(id)) {
    return results[0]
  }

  // If array was passed, return array of results
  return results
}

export const unsetAndDeleteAllItems = async (
  type: string,
  query: string,
  referenceFieldsToUnset?: string | string[],
) => {
  if (referenceFieldsToUnset) {
    referenceFieldsToUnset = Array.isArray(referenceFieldsToUnset) ? referenceFieldsToUnset : [referenceFieldsToUnset]
  }

  const sanity = getSanityClient()
  const transaction = sanity.transaction()

  // eslint-disable-next-line no-console
  console.log(`Deleting all ${type}s...`)

  /*
  Unset references, save
  */

  if (referenceFieldsToUnset?.length) {
    const unsetReferencesTransaction = sanity.transaction()
    const itemsWithRefs = await sanity.fetch(`${query}{
    _id,
    "refs": count(*[references(^._id)])
  }`)
    for (const ep of itemsWithRefs) {
      if (ep.refs > 0) {
        unsetReferencesTransaction.patch(ep._id, p => p.unset(referenceFieldsToUnset as string[]))
      }
    }
    await unsetReferencesTransaction.commit()
  }

  /*
  Delete All
  */

  const items = await sanity.fetch(query)

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  items.forEach((item: any) => {
    transaction.delete(item._id)
  })

  const result = await transaction.commit()

  // eslint-disable-next-line no-console
  console.log(`Deleted ${result?.documentIds?.length} ${type}s`)
}

export const createAllDocumentsFromCraft = async (
  query: string,
  resultsKey: string,
  sanityDocType: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  filterItemsCallback?: (items: any[]) => any[],
) => {
  if (!resultsKey || !query) {
    throw new Error('Key and query are required in createAllDocumentsFromCraft')
  }

  // eslint-disable-next-line no-console
  console.log(`Fetching all documents for type: ${sanityDocType}`)

  const results = await sendCraftRequest(query)

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getNestedValue = (obj: any, path: string): any => {
    // Split the path into an array of keys
    const keys = path.split('.')

    // Start with the initial object
    let current = obj

    // Loop through each key in the path
    for (const key of keys) {
      // If current is null/undefined or key doesn't exist, return null
      if (current === null || current === undefined || !Object.prototype.hasOwnProperty.call(current, key)) {
        return null
      }
      // Move to the next nested level
      current = current[key]
    }

    return current
  }

  let values = getNestedValue(results?.data, resultsKey)

  if (!values) {
    throw new Error(`Value is required in createAllDocumentsFromCraft. resultsKey: ${resultsKey}`)
  }

  if (filterItemsCallback) {
    values = filterItemsCallback(values)
  }

  // eslint-disable-next-line no-console
  console.log(`Uploading ${values?.length} ${sanityDocType}s to Sanity...`)

  const client = getSanityClient()

  // Create a transaction
  const transaction = client.transaction()

  // Add all create operations to the transaction
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  values.forEach((craftItem: any) => {
    transaction.createIfNotExists({
      _type: sanityDocType,
      _id: getSanityIdFromCraftId(craftItem.id, sanityDocType),
      craftData: {
        id: craftItem.id,
        importedFromCraft: true,
      },
    })
  })

  // Commit the transaction
  await transaction.commit()

  // eslint-disable-next-line no-console
  console.log(`Created all ${sanityDocType}s in Sanity.`)
}

const TEST_COLLECTIONS_BY_TYPE = {
  recipe: {
    slug: 'test-recipe-collection-do-not-delete',
    items: [],
  },
  radioEpisode: {
    slug: 'test-radio-episodes-collection-do-not-delete',
    items: [],
  },
  tvEpisode: {
    slug: 'test-tv-episodes-collection-do-not-delete',
    items: [],
  },
  magazineIssue: {
    slug: 'test-magazine-issues-collection-do-not-delete',
    items: [],
  },
  article: {
    slug: 'test-articles-collection-do-not-delete',
    items: [],
  },
}

export const buildTestCollections = async () => {
  const client = getSanityClient()

  const numberOfItemsToAdd = 40

  // eslint-disable-next-line no-console
  console.log('Building test collections...')

  for (const type of Object.keys(TEST_COLLECTIONS_BY_TYPE)) {
    const slug = TEST_COLLECTIONS_BY_TYPE[type as keyof typeof TEST_COLLECTIONS_BY_TYPE].slug
    const results = await client.fetch('*[_type == "collection" && slug.current == $slug][0]{_id,slug, title}', {
      slug,
    })

    if (!results._id) {
      throw new Error(`No collections found for type: ${type}`)
    }

    const randomItems = await client.fetch(
      `*[_type == "${type}" && craftData.importedFromCraft == true][0...${numberOfItemsToAdd}]{_id}`,
    )

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    TEST_COLLECTIONS_BY_TYPE[type as keyof typeof TEST_COLLECTIONS_BY_TYPE].items = randomItems.map(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (item: any) => item._id,
    )

    // Update collection with new items
    await client
      .patch(results._id)
      .set({
        items: TEST_COLLECTIONS_BY_TYPE[type as keyof typeof TEST_COLLECTIONS_BY_TYPE].items.map(
          (id: string, index: number) => ({
            _type: 'reference',
            _ref: id,
            _key: `${id}-${index}`,
          }),
        ),
      })
      .commit()

    // eslint-disable-next-line no-console
    console.log(
      `✅ Updated ${type} test collection with ${TEST_COLLECTIONS_BY_TYPE[type as keyof typeof TEST_COLLECTIONS_BY_TYPE].items.length} items`,
    )
  }

  // eslint-disable-next-line no-console
  console.log('Added items to collections')
}

export const emptyTestCollections = async () => {
  const client = getSanityClient()

  // eslint-disable-next-line
  console.log('Emptying test collections...')

  for (const type of Object.keys(TEST_COLLECTIONS_BY_TYPE)) {
    const slug = TEST_COLLECTIONS_BY_TYPE[type as keyof typeof TEST_COLLECTIONS_BY_TYPE].slug
    const results = await client.fetch('*[_type == "collection" && slug.current == $slug][0]{_id,slug, title}', {
      slug,
    })

    if (!results._id) {
      throw new Error(`No collections found for type: ${type}`)
    }

    await client
      .patch(results._id)
      .set({
        items: [],
      })
      .commit()

    // eslint-disable-next-line no-console
    console.log(`Emptied ${type} test collection`)
  }

  // eslint-disable-next-line no-console
  console.log('Emptied all test collections')
}

export const getAllSubcategoriesByIdInSanity = async () => {
  const client = getSanityClient()
  const subcategories = await client.fetch('*[_type == "subcategory"]{_id, slug}')

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const slugHashmap: any = {}

  /* eslint-disable @typescript-eslint/no-explicit-any */
  subcategories.forEach((subcategory: any) => {
    if (!slugHashmap[subcategory?._id as keyof typeof slugHashmap]) {
      slugHashmap[subcategory?._id as keyof typeof slugHashmap] = subcategory
    }
  })

  return slugHashmap
}

export const patchAllDocsById = async (formattedDocsForSanity: any) => {
  const client = getSanityClient()
  const transaction = client.transaction()

  formattedDocsForSanity.forEach((doc: any) => {
    transaction.createIfNotExists(doc).patch(doc._id, patch => {
      return patch.set(doc)
    })
  })

  await transaction.commit() // Sends all patches at once
}
