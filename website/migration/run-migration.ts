/* eslint-disable no-console */

import recipeScript from './types/recipe'
import personScript from './types/person'
import categoryScript from './types/categories'
import radioEpisodeScript from './types/radioEpisode'
import tvEpisodeScript from './types/tvEpisode'
import magazineIssueScript from './types/magazineIssue'
import articleScript from './types/article'
import stationFinderScript from './types/stationFinder'
import fs from 'fs'
import { buildTestCollections, emptyTestCollections } from './utils/sanity'

// MIGRATION TODO:
/*
--------------
RECIPES
--------------
RADIO EPISODES
Set default links for pandora, spotify, apple, youtube, in Site Settings
--------------
TV EPISODES
--------------
*/

const OPTIONS = {
  perPage: 100,
}

const createAllDocuments = async () => {
  console.log('===============================================')
  console.log('Creating all documents In Sanity from Craft.')
  await categoryScript.main()
  console.log('===============================================')
  await personScript.main()
  console.log('===============================================')
  await radioEpisodeScript.createDocumentsInSanity()
  console.log('===============================================')
  await tvEpisodeScript.createDocumentsInSanity()
  console.log('===============================================')
  await recipeScript.createDocumentsInSanity()
  console.log('===============================================')
  await magazineIssueScript.createDocumentsInSanity()
  console.log('===============================================')
  await articleScript.createDocumentsInSanity()
  console.log('===============================================')
  console.log('Created all documents In Sanity from Craft.')
}

const deleteAll = async () => {
  await emptyTestCollections()
  console.log('===============================================')
  await articleScript.deleteAll()
  console.log('===============================================')
  await magazineIssueScript.deleteAll()
  console.log('===============================================')
  await radioEpisodeScript.deleteAll()
  console.log('===============================================')
  await tvEpisodeScript.deleteAll()
  console.log('===============================================')
  await recipeScript.deleteAll()
  console.log('===============================================')
  await personScript.deleteAll()
  console.log('===============================================')
  await categoryScript.deleteAll()
  console.log('===============================================')
  console.log('Deleted all.')
}

const buildAll = async () => {
  // Clear the file before starting new uploads
  await fs.writeFileSync('migration/debug/images_that_were_uploaded.txt', '')
  await fs.writeFileSync('migration/debug/weird_slugs.txt', '')
  console.log('===============================================')
  await radioEpisodeScript.main(OPTIONS)
  console.log('===============================================')
  await tvEpisodeScript.main(OPTIONS)
  console.log('===============================================')
  await magazineIssueScript.main(OPTIONS)
  console.log('===============================================')
  await articleScript.main(OPTIONS)
  console.log('===============================================')
  await recipeScript.main(OPTIONS)
  console.log('===============================================')
  await recipeScript.syncWithAirtable()
  console.log('===============================================')
  await stationFinderScript.main()
  console.log('===============================================')
  console.log('Done building all content.')
}

// Add command line execution
// npx ts-node migration/run-migration.ts buildAll
// npx ts-node migration/run-migration.ts deleteAll
const main = async () => {
  if (require.main === module) {
    const command = process.argv[2]

    if (command === 'deleteAll') {
      try {
        await deleteAll()
        process.exit(0)
      } catch (error) {
        console.error('Error running delete:', error)
        process.exit(1)
      }
    } else if (command === 'buildAll') {
      try {
        await buildAll()
        process.exit(0)
      } catch (error) {
        console.error('Error running buildAll:', error)
        process.exit(1)
      }
    } else if (command === 'createAllDocuments') {
      try {
        await createAllDocuments()
        process.exit(0)
      } catch (error) {
        console.error('Error running buildAll:', error)
        process.exit(1)
      }
    } else if (command === 'buildTestCollections') {
      try {
        await buildTestCollections()
        process.exit(0)
      } catch (error) {
        console.error('Error running buildTestCollections:', error)
        process.exit(1)
      }
    } else if (command === 'emptyTestCollections') {
      try {
        await emptyTestCollections()
        process.exit(0)
      } catch (error) {
        console.error('Error running emptyTestCollections:', error)
        process.exit(1)
      }
    } else {
      console.error('Please specify a command (deleteAll or buildAll)')
      process.exit(1)
    }
  }
}

main()
