import { groq } from 'next-sanity'
import { sendCraftRequest } from '../utils/craftCms'
import { getSanityClient } from '../utils/sanity'

export const main = async () => {
  // eslint-disable-next-line no-console
  console.log('Migrating over station finder...')

  const allStations = await sendCraftRequest(`
    {
      entries(id:["1170"]) {
        id
        stationFinder {
          stateName
          stationList {
            city
            station
            schedule
          }
        }
      }
    }
  `)

  const value = allStations?.data?.data?.entries[0]?.stationFinder
  if (!value || !value?.length) {
    console.error('No value found in stationFinder')
    return
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formatForSanity = value.map((item: any, index: number) => {
    return {
      _key: `${item.stateName}-${index}`,
      _type: 'object',
      stateName: item.stateName,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      stationList: item.stationList
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .map((station: any) => {
          if (!station.city || !station.station || !station.schedule) {
            return null
          }

          return {
            _key: `${item.stateName}-${station.city}-${station.station}-${station.schedule}-${index}`,
            _type: 'object',
            city: station.city,
            station: station.station,
            schedule: station.schedule,
          }
        })
        .filter(Boolean),
    }
  })

  const client = getSanityClient()
  const siteSettingsDoc = await client.fetch(groq`
    *[_type == "siteSettings"][0]{
      _id
    }
  `)

  if (!siteSettingsDoc) {
    console.error('No site settings doc found')
    return
  }

  const result = await client
    .patch(siteSettingsDoc._id)
    .set({
      stationFinder: formatForSanity,
    })
    .commit()

  if (result) {
    // eslint-disable-next-line no-console
    console.log('Station finder updated successfully')
  } else {
    console.error('Station finder update failed')
  }
}

const moduleExports = {
  main,
}

export default moduleExports
