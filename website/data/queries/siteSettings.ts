import { groq } from 'next-sanity'
import link from './link'
import imageAsset from './imageAsset'

const siteSettingsQueryLine = groq`*[_type == "siteSettings"][0]`

export const headerNavFields = groq`
  ${link.fragment('signupLink')},
  ${link.fragment('loginLink')},
  bowtieImage,
  headerNav[]{
    ${link.fragment('linkData')},
    title,
    isLink,
    link,
    subMenuGroups[]{
      items[]{
        _type,
        title,
        ${link.fragment('titleLink')},
        ${link.fragment('linkData')}
      }
    }
  }
`
export const headerNavQuery = groq`${siteSettingsQueryLine}{
  ${headerNavFields}
}`

export const footerNavFields = groq`
  footerNav[]{
    title,
    isLink,
    ${link.fragment('link')},
    links[]{
      ${link.fragment('linkData')},
    }
  }
`

export const footerNewsletterFields = groq`
  footerNewsletter{
    title,
    description,
    placeholderText,
    buttonText
  }
`

export const socialLinksQuery = groq`${siteSettingsQueryLine}{
  socialLinks[]{
    platform,
    url
  }
}`

export const footerLegalLinksFields = groq`
  footerLegalLinks[]{
    text,
    isLink,
    ${link.fragment('linkData')},
  }
`

export const globalAnnouncementFields = groq`
  globalAnnouncement{
    enabled,
    text,
    link,
    backgroundColor,
    textColor,
    displayUntil
  }
`

export const newsletterPopupQuery = groq`${siteSettingsQueryLine}{
  newsletterPopup{
    enabled,
    title,
    description,
    image,
    buttonText,
    delay,
    frequency
  }
}`

export const membershipPromoQuery = groq`${siteSettingsQueryLine}{
  membershipPromo{
    enabled,
    title,
    description,
    image,
    buttonText,
    buttonLink,
    discount,
    expiration
  }
}`

export const cookieConsentQuery = groq`${siteSettingsQueryLine}{
  cookieConsent{
    enabled,
    text,
    acceptButtonText,
    settingsButtonText,
    privacyPolicyLink
  }
}`

export const siteSettingsSocialSharingFields = groq`
  socialSharing{
    keywords,
    ${imageAsset.fragment('favicon')},
    ${imageAsset.fragment('defaultOpenGraphImage')}
  }
`

export const siteSettingsMetaDataForPages = groq`
  "globalMetaData": ${siteSettingsQueryLine}{
    title,
    description,
    ...${siteSettingsSocialSharingFields}
  }
`

export const siteSettingsQuery = groq`${siteSettingsQueryLine}{
  _id,
  title,
  description,
  footerLogo,
  contactInfo,
  copyrightText,
  ${headerNavFields},
  ${footerNavFields},
  ${footerNewsletterFields},
  socialLinks,
  ${footerLegalLinksFields},
  ${globalAnnouncementFields},
  newsletterPopup,
  membershipPromo,
  ${siteSettingsSocialSharingFields},
  cookieConsent,
  stationFinder[] {
    stateName,
    stationList[] {
      city,
      station,
      schedule
    }
  }
}`
