import { groq } from 'next-sanity'
import link from './link'
import imageAsset from './imageAsset'
import button from './button'

const CARD_FIELDS_DESCRIPTION_CARD_LIMIT = 250

export const getStringFromRichText = (richTextFieldName: string, numberOfCharacters: number) => {
  return `array::join(string::split((pt::text(${richTextFieldName})), "")[0..${numberOfCharacters}], "")`
}

export const getRichTextFields = ({
  childrenString = '',
  markDefsString = '',
  additionalFields = '',
  // eslint-disable-next-line
}: any) => {
  return groq`
    _key,
    _type,
    children[] {
      ...,
      _type == "link" => {
        ${link.fields}
      },
      ${childrenString}
    },
    style,
    listItem,
    level,
    markDefs[]{
      ...,
      _type == "link" => {
        ${link.fields}
      },
      ${markDefsString}
    },
    _type == "button" => {
      ${button.fields}
    },
    ${additionalFields}

  `
}

export const richTextFields = groq`
  ${getRichTextFields({})}
`

export const personFields = groq`
  name,
  slug,
  ${imageAsset.fragment('image')},
  bio[]{${richTextFields}},
  designation
`

export const cardFieldsByType = {
  recipe: groq`
    _type,
    title,
    "slug": slug.current,
    "image": images[0] {
      ${imageAsset.fields}
    },
    "description": coalesce(
      ${getStringFromRichText('summary', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)},
      ${getStringFromRichText('body', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)}, 
      ''),
    "starsRatingProps": {
      "count": 100,
      "percent": 0.8
    },
  `,
  article: groq`
    _type,
    title,
    "slug": slug.current,
    "image": image {
      ${imageAsset.fields}
    },
    "description": coalesce(
      ${getStringFromRichText('subheader', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)}, 
      ${getStringFromRichText('lead', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)}, 
      ${getStringFromRichText('body', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)},
      ''),
    "author": author->name,
  `,
  magazineIssue: groq`
    _type,
    title,
    "slug": slug.current,
    "description": coalesce(featuredSubtitle, ''),
    "imageOrientation": "magazine",
    "image": image {
      ${imageAsset.fields}
    },
    "date": publishedAt,
  `,
  classCard: groq`
    _type,
    title,
    "slug": slug.current,
    "description": ${getStringFromRichText('description', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)},
    "image": mainImage {
      ${imageAsset.fields}    
    },
    "instructor": instructor->name,
    date,
    location,
    price,
    skillLevel,
  `,
  class: groq`
    _type,
    title,
    "description": ${getStringFromRichText('description', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)},
    "slug": slug.current,
    "image": mainImage {
      ${imageAsset.fields}    
    },
    "author": instructor->name,
    date,
  `,
  tvEpisode: groq`
    _type,
    title,
    "slug": slug.current,
    "image": image {
      ${imageAsset.fields}
    },
    "description": coalesce(
      ${getStringFromRichText('summary', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)}, 
      ${getStringFromRichText('body', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)}, 
      ''),
    "episodeAndDateProps": {
      "season": "2",
      "episode": "8",
      "date": publishedAt,
    },
  `,
  radioEpisode: groq`
    _type,
    title,
    "slug": slug.current,
    "description": coalesce(
      ${getStringFromRichText('summary', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)}, 
      ${getStringFromRichText('body', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)}, 
      ${getStringFromRichText('legacyBody', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)}, 
      ''),
    "image": image {
      ${imageAsset.fields}
    },
    "episodeAndDateProps": {
      "episode": "1",
      "date": airDate,
    },
  `,
  product: groq`
    _type,
    title,
    "slug": slug.current,
    "description": ${getStringFromRichText('description', CARD_FIELDS_DESCRIPTION_CARD_LIMIT)},
    "image": productImages[0] {
      ${imageAsset.fields}
    },
    "price": {
      "default": price,
      "memberPrice": salePrice,
    },
  `,
}

// eslint-disable-next-line
export const cardFields = Object.keys(cardFieldsByType as any)
  .map(type => `_type == "${type}" => { ${cardFieldsByType[type as keyof typeof cardFieldsByType]} }`)
  .join(',')
