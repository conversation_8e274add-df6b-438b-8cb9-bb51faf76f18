import { groq } from 'next-sanity'
import { getRichTextFields } from '../_shared'
import cmsSettings from '../cmsSettings'
import cardItems from '../cardItems'
import sidebar from '../sidebar'
import tipObject from '../tipObject'
import button from '../button'
import cardItem from '../cardItem'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  content[]{${getRichTextFields({
    additionalFields: groq`
      _type == "tip" => {
        _type,
        ${tipObject.fragment('content')}
      },
      _type == "button" => {
        _type,
        ${button.fields}
      }
    `,
  })}},
  "textAlignment": coalesce(textAlignment, 'left'),
  "contentWidth": coalesce(contentWidth, 'full'),
  "hasSidebar": coalesce(hasSidebar, false),
  "sidebarSide": coalesce(sidebarSide, 'left'),
  sidebarContent[]{
    _type,
    ${sidebar.fields}
  }
`
