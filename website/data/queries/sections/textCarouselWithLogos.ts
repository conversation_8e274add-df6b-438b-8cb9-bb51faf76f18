import { groq } from 'next-sanity'
import cmsSettings from '../cmsSettings'
import imageAsset from '../imageAsset'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  carouselItems[] {
    title,
    subtitle
  },
  logoItems[] {
    ${imageAsset.fragment('image')},
    desktopWidth,
    mobileWidth
  }
`

export const fragment = (name = 'textCarouselWithLogos') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
