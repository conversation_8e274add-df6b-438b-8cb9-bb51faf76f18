import { groq } from 'next-sanity'
import * as fragments from './sections/index'

const ALL_SECTIONS: string[] = [
  /* INJECT_TYPE */
  'searchBarAndContent',
  'adSection',
  'subscriptionPlansSection',
  'showAllPageSectionExamples',
  'tipSection',
  'imageCarouselAndText',
  'logoSliderSection',
  'textAndImageSection',
  'textAndPointsWithImageSection',
  'headerWithSubnav',
  'multiColumnContent',
  'contentList',
  'exampleSection',
  'richTextSection',
  'classCalendar',
  'textAndStaticImageSection',
  'bigImageSection',
  'imageAndRichTextWithButton',
  'titleTextAndCtaSection',
  'threeColumnContentSection',
  'eyebrowAndTitleSection',
  'textCarouselWithLogos',
  'lineLinkSection',
  'nextPreviousSection',
]

const fragmentsCopy: {
  // eslint-disable-next-line
  [key: string]: any
} = { ...fragments }

const sectionFields = ALL_SECTIONS.map(typeName => {
  if (fragmentsCopy[typeName as keyof typeof fragmentsCopy]) {
    return `_type == "${typeName}" => { ${fragmentsCopy[typeName as keyof typeof fragmentsCopy].fields} }\n`
  }
}).join(',')

const referenceFields = groq`
  _type == "reference" => { 
    ...(@->) {
      ...section[][0] {
        ${sectionFields},
      }
    }
  }
`

const totalFieldsWithReferences = groq`
  ${sectionFields},
  ${referenceFields}
`

export const fields = totalFieldsWithReferences

const exported = {
  fields,
}

export default exported
