/* eslint-disable */
import { createClient, groq } from 'next-sanity'
import axios from 'axios'
const SANITY_CONFIG = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION,
  token: process.env.SANITY_MIGRATION_TOKEN,
  useCdn: false,
}

const getShopifyProductsFromCollectionId = async (collectionId: string) => {
  if (!collectionId?.length) return []
  const endpoint = `${process.env.SHOPIFY_STORE_URL}/admin/api/2020-04/collections/${collectionId}/products.json`
  const response = await axios.get(endpoint, {
    headers: {
      'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_API_TOKEN,
    },
  })
  return response?.data?.products?.map((product: any) => product?.id || null).filter(Boolean) || []
}

const COLLECTION_PREFIX = 'shopifyCollection-'

const sanityClient = createClient({
  ...SANITY_CONFIG,
})

const COLLECTION_QUERY = groq`_type == "shopifyCollection" && store.isDeleted == false`
const MAX_PRODUCTS_PER_COLLECTION = 20
const COLLECTION_FIELDS = groq`
  _id
`

const executeBatchedPromises = async <T>(
  items: T[],
  executor: (item: T) => Promise<any>,
  options?: {
    batchSize?: number
    delayBetweenBatches?: number
    onBatchComplete?: (batchNumber: number, totalBatches: number) => void
  },
) => {
  const batchSize = options?.batchSize || 50
  const delayBetweenBatches = options?.delayBetweenBatches || 1000
  const batches = []

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    batches.push(batch)
  }

  process.stdout.write('\n')

  let i = 0
  for (const batch of batches) {
    await Promise.all(batch.map(executor))
    await new Promise(resolve => setTimeout(resolve, delayBetweenBatches))

    if (options?.onBatchComplete) {
      options.onBatchComplete(i + 1, batches.length)
    } else {
      process.stdout.write(`\rProcessed batch ${i + 1} of ${batches.length}`)
    }
    i++
  }

  process.stdout.write('\n')
}

export const updateCollectionsWithProductReferences = async () => {
  const client = sanityClient

  const collections = await client.fetch(groq`
    *[${COLLECTION_QUERY}] {
      ${COLLECTION_FIELDS}
    }
  `)

  const allShopifyProducts = await client.fetch(groq`
    *[_type == "shopifyProduct"] {
      _id
    }
  `)

  const shopifyProductsHash: any = {}
  allShopifyProducts.forEach((product: any) => {
    shopifyProductsHash[product._id] = true
  })

  const collectionIds = collections
    .map((collection: any) => {
      const withoutPrefix = collection._id.replace(COLLECTION_PREFIX, '')
      if (!withoutPrefix?.length || withoutPrefix.includes('drafts')) return null
      return withoutPrefix
    })
    .filter(Boolean)

  const findProductsTransactions = []
  const productsToAddByCollectionId: any = {}

  for (const collectionId of collectionIds) {
    findProductsTransactions.push({
      fire: async () => {
        const products = await getShopifyProductsFromCollectionId(collectionId)

        if (collectionId) {
          const productsFormatted = products
            .map((productId: any, index: number) => {
              if (!shopifyProductsHash[`shopifyProduct-${productId}`]) {
                return null
              }

              return {
                _type: 'reference',
                _ref: `shopifyProduct-${productId}`,
                _key: `shopifyProduct-${productId}-${index}`,
              }
            })
            .filter(Boolean)

          // transactions.push(
          //   sanityClient.patch(`${COLLECTION_PREFIX}${collectionId}`).set({ 'store.products': productsFormatted }),
          // )

          productsToAddByCollectionId[collectionId] = productsFormatted
        }
      },
    })
  }

  try {
    await executeBatchedPromises(findProductsTransactions, item => item.fire(), {
      onBatchComplete: (batchNumber, totalBatches) => {
        process.stdout.write(`\rProcessed finding products for batch ${batchNumber} of ${totalBatches}`)
      },
      delayBetweenBatches: 2000,
    })
  } catch (error) {
    console.error(error)
  }

  console.log(`${Object.keys(productsToAddByCollectionId).length} collections to update!`)

  const transactions = []

  const keys = Object.keys(productsToAddByCollectionId)

  for (const key of keys) {
    const itemToUpdate = productsToAddByCollectionId[key]
    if (itemToUpdate) {
      transactions.push(sanityClient.patch(`${COLLECTION_PREFIX}${key}`).set({ 'store.products': itemToUpdate }))
    }
  }

  try {
    await executeBatchedPromises(transactions, item => item.commit(), {
      onBatchComplete: (batchNumber, totalBatches) => {
        process.stdout.write(`\rProcessed updating collections (${batchNumber} of ${totalBatches})`)
      },
      delayBetweenBatches: 1000,
    })
  } catch (error) {
    console.error(error)
  }

  console.log('Added product references to collections.')
}

// updateCollectionsWithProductReferences()
