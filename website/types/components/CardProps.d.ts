import { SanityLink } from '@/types/sanity/SanityLink'

type CardPortraitImageOrientation = 'square' | '4:3' | '16:9' | 'magazine'
type CardLandscapeImageOrientation = 'square-xs' | 'square-sm' | 'square-md' | 'square-lg' | 'magazine' | '4:3' | '16:9'
type CardOrientation = 'portrait' | 'landscape'
type CardImageSide = 'left' | 'right'

interface CardPrice {
  default: number
  memberPrice?: number
}

type CardProps = {
  _type?: string
  className?: string
  title: string
  titleSize?: 'sm' | 'md' | 'lg'
  description?: string
  image?: SanityImage
  link?: SanityLink
  capLines?: boolean | number
  imageSide?: CardImageSide
  padding?: boolean
  price?: CardPrice
  author?: string
  date?: string
  additionalText?: string
  showDateSquare?: boolean
  allowFavoriting?: boolean
  imageColumns?: number
  arrowButtonText?: string
  episodeAndDateProps?: Pick<EpisodeAndDateProps, 'season' | 'episode' | 'size' | 'date'>
  starsRatingProps?: Pick<StarsRatingProps, 'count' | 'percent'>
  tagProps?: Pick<TagProps, 'text' | 'size'>
} & (
  | {
      orientation?: 'landscape'
      imageOrientation?: CardLandscapeImageOrientation
    }
  | {
      orientation?: 'portrait'
      imageOrientation?: CardPortraitImageOrientation
    }
)

export type SanityCard = CardProps & {
  slug: string
}
