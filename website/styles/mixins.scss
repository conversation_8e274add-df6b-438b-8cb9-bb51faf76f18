@use 'sass:list';
@use 'sass:map';
@import 'vars';

// ================================================
// GPU fix
// ================================================
@mixin gpu {
  backface-visibility: hidden;
  transform-style: preserve-3d;
  perspective: 1000px;

  .safari & {
    perspective: none; // otherwise text and icons become blurry
  }
}

// ================================================
// Same height/width box
// ================================================
@mixin box($width, $height: $width) {
  width: $width;
  height: $height;
}

// ================================================
// REM calc
// ================================================
@function px($px) {
  @return $px * 0.0625rem;
}

// ================================================
// Top left positioning
// ================================================
@mixin position-0 {
  top: 0;
  left: 0;
}

// ================================================
// Full width/height positioning
// ================================================
@mixin position-100($pos: relative) {
  position: $pos;
  @include position-0;
  @include box(100%);
}

// ================================================
// Center things horizontally in flexbox
// ================================================
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// ================================================
// Center things vertically in flexbox
// ================================================
@mixin flex-center-vert {
  display: flex;
  align-items: center;
}

// ================================================
// Center something with abs/fixed positioning
// ================================================
@mixin transform-center {
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}

// ================================================
// Z-Index for main components
// ================================================
@mixin z-index($id) {
  z-index: list.index($elements, $id) + 100;
}

// ================================================
// Media queries
// ================================================
@mixin bp($size, $is-max-width: false) {
  @if $is-max-width {
    @media (max-width: #{map.get($layout, $size)}px) {
      @content;
    }
  } @else {
    @media (min-width: #{map.get($layout, $size)}px) {
      @content;
    }
  }
}

@mixin bp-custom($query, $is-max-width: false) {
  @if $is-max-width {
    @media (max-width: #{$query}) {
      @content;
    }
  } @else {
    @media (min-width: #{$query}) {
      @content;
    }
  }
}

@mixin bp-height($px, $is-max-width: false) {
  @if $is-max-width {
    @media (max-height: #{$px}px) {
      @content;
    }
  } @else {
    @media (min-height: #{$px}px) {
      @content;
    }
  }
}

// ================================================
// Hover
// ================================================
@mixin hover {
  @media (pointer: fine) {
    &:hover {
      @content;
    }
  }
}

// ================================================
// Reset ul
// ================================================
@mixin reset-ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

// ================================================
// Reset button
// ================================================
@mixin reset-button {
  border: none;
  cursor: pointer;
  background-color: transparent;
  padding: 0;
  text-align: inherit;
  letter-spacing: inherit;
  /* stylelint-disable property-disallowed-list */
  font-size: inherit;
  text-transform: inherit;
  /* stylelint-enable property-disallowed-list */
  display: inline-block;
  margin: 0;
  text-decoration: none;
  appearance: none;
  color: currentColor;
}

// ================================================
// Grid
// ================================================

@mixin grid-12 {
  display: grid;
  gap: var(--gutter);
  grid-template-columns: repeat(12, 1fr);
}

// ================================================
// Screen Reader Text
// ================================================

@mixin screen-reader-text {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

// ================================================
// Rich Text
// ================================================

@mixin rich-text-base-styling {
  [data-p],
  [data-h2],
  [data-ul],
  [data-h3] {
    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  [data-p],
  [data-ul] {
    margin: 1em 0;

    [data-link] {
      text-decoration: underline;
    }
  }
}

// ================================================
// Hide scrollbar
// ================================================

@mixin hide-scrollbar {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
  &::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }
}
