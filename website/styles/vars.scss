@use 'sass:map';

// ================================================
// Breakpoints
// ================================================

$layout-tablet: 967;
$layout: (
  mobile: 768,
  tablet: $layout-tablet,
  navigation: $layout-tablet,
  laptop: 1200,
  desktop: 1512,
  xl: 1800,
);
$layout-mobile: map.get($layout, mobile) + px;
$layout-tablet: map.get($layout, tablet) + px;
$layout-navigation: map.get($layout, navigation) + px;
$layout-laptop: map.get($layout, laptop) + px;
$layout-desktop: map.get($layout, desktop) + px;
$layout-xl: map.get($layout, xl) + px;

// ================================================
// Colors
// ================================================

// General
$white: #fff;
$black: #000;
$parchment: #fff;
$gray-quiet: #f6f4ec;
$highlight-light: #fdffd5;
$highlighter: #f1f874;
$loud-warm: #cd4a26;
$loud-warm-darker: #a33b1e;
$navy-dark: #1b2d41;
$navy-light: #2c3b4b;
$slate: #5a6d82;

// ================================================
// Grid
// ================================================
$gutter: px(32);
$page-gutter: px(96);
$page-gutter-tablet: px(40);
$page-gutter-mobile: px(16);

// ================================================
// General
// ================================================
$transition-short: 0.25s;
$section-spacing-desktop: #{px(80)};
$section-spacing-mobile: #{px(60)};
$section-max-width: px(1440);
$header-height: px(80);
$header-height-mobile: px(64);

// ================================================
// Fonts
// ================================================
$display-font-name: 'Terza Display Web';
$display-fonts: $display-font-name, sans-serif;
$body-font-variable: --font-dm-sans;

// ================================================
// Components
// ================================================
/* stylelint-disable */
$image-carousel-aspect-ratio: #{616/462};
$aspect-ratio-square: #{1/1};
$aspect-ratio-4-3: #{4/3};
$aspect-ratio-16-9: #{16/9};
$aspect-ratio-magazine: #{160/187};
/* stylelint-enable */

// ================================================
// z-index data (see z-index mixin)
// ================================================
$elements:
  main, bowtie, header, submenu, mobile-header, mobile-menu-overlay, navigation, mobile-in-progress, preview-button;
