'use client'

import classnames from 'classnames'
import styles from './NextPrevious.module.scss'
import SanityImage from '@/components/SanityImage/SanityImage'
import EpisodeAndDate from '@/components/EpisodeAndDate/EpisodeAndDate'
import Link from '@/components/Link/Link'
import Button from '@/components/Button/Button'

const NextPrevious = ({
  className,
  direction = 'next',
  title,
  image,
  episodeTitle,
  date,
  link,
  labelType,
}: NextPreviousProps) => {
  const isNext = direction === 'next'
  const labelText = isNext ? `Next${labelType ? ` ${labelType}` : ''}` : `Previous${labelType ? ` ${labelType}` : ''}`

  if (!title || !image || !link) return null

  return (
    <Link link={link}>
      <div
        className={classnames(styles.NextPrevious, className)}
        data-next-previous-direction={direction}
      >
        <div className={styles.content}>
          <div className={styles.imageWrapper}>
            <SanityImage
              source={image}
              isCover
              columns={{
                md: 3,
                sm: 5,
              }}
            />
          </div>
          <div className={styles.textContent}>
            <Button
              className={styles.button}
              element="span"
              label={labelText}
              icon={direction === 'next' ? 'arrowRight' : 'arrowLeft'}
              iconPosition={direction === 'next' ? 'right' : 'left'}
              style="bare"
              bareColor="slate"
              bareFont="eyebrow"
              disableHoverAnimation
            />
            <h3 className={styles.title}>{title}</h3>
            {(episodeTitle || date) && (
              <EpisodeAndDate
                hasIcon={false}
                size="sm"
                date={date}
                {...(episodeTitle || {})}
              />
            )}
          </div>
        </div>
      </div>
    </Link>
  )
}

NextPrevious.displayName = 'NextPrevious'

export default NextPrevious
