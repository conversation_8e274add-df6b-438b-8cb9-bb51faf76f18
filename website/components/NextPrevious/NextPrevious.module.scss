.NextPrevious {
  display: flex;
  width: 100%;
}

.content {
  display: flex;
  align-items: center;

  [data-next-previous-direction='next'] & {
    flex-direction: row-reverse;
    justify-content: space-between;
  }

  [data-next-previous-direction='previous'] & {
    flex-direction: row;
    justify-content: space-between;
  }
}

.imageWrapper {
  width: px(128);
}

.textContent {
  display: flex;
  flex-direction: column;

  [data-next-previous-direction='next'] & {
    align-items: flex-end;
    text-align: right;
    margin-right: px(12);
  }

  [data-next-previous-direction='previous'] & {
    align-items: flex-start;
    text-align: left;
    margin-left: px(12);
  }

  @include bp(tablet) {
    width: px(260);

    [data-next-previous-direction='next'] & {
      margin-right: px(16);
    }

    [data-next-previous-direction='previous'] & {
      margin-left: px(16);
    }
  }
}

.title {
  @include font-heading-sm;
  color: var(--navy-dark);
}
