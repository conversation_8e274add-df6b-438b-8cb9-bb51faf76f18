.ItemLabelList {
  width: 100%;
}

.list {
  @include reset-ul;
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: px(24);

  &:first-child {
    margin-top: 0;
  }
}

.item {
  margin-bottom: px(12);

  @include bp(tablet) {
    margin-bottom: px(24);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.labelValueContainer {
  width: 100%;
}

.label {
  @include font-heading-eyebrow-md;
  color: var(--navy-dark);
  margin-bottom: px(4);
}

.value {
  @include font-text-lg;
  color: var(--navy-dark);
  font-weight: 700;
}
