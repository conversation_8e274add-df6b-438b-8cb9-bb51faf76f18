import { PageRendererProps } from '@/types/PageRenderer'
import { Section } from './Section'

/**
 * Renders a page with its sections, handling both direct sections and shared sections
 */
export default function PageRenderer({ content }: PageRendererProps) {
  content = content
    .map((section: SanityAllSectionTypes) => {
      if (section?.cmsSettings?.isHidden) {
        return null
      }

      return section
    })
    .filter(Boolean)

  return (
    <>
      {content?.map((section: SanityAllSectionTypes, index: number) => (
        <Section
          key={index}
          section={section}
        />
      ))}
    </>
  )
}
