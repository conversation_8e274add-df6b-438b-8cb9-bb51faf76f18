import type { Meta, StoryObj } from '@storybook/react'
import EpisodeAndDate from './EpisodeAndDate'

const meta = {
  title: 'Components/EpisodeAndDate',
  component: EpisodeAndDate,

  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'radio',
      options: ['sm', 'md'],
    },
    hasIcon: {
      control: 'boolean',
    },
  },
} satisfies Meta<typeof EpisodeAndDate>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    season: 8,
    episode: 821,
    date: '2025-01-29T00:00:00.000Z',
    hasIcon: true,
    size: 'md',
  },
}

export const SmallSize: Story = {
  args: {
    ...Default.args,
    size: 'sm',
  },
}

export const NoIcon: Story = {
  args: {
    ...Default.args,
    hasIcon: false,
  },
}

export const SeasonOnly: Story = {
  args: {
    season: 8,
    date: '2025-01-29T00:00:00.000Z',
  },
}

export const EpisodeOnly: Story = {
  args: {
    episode: 821,
    date: '2025-01-29T00:00:00.000Z',
  },
}

export const NoDate: Story = {
  args: {
    season: 8,
    episode: 821,
  },
}

export const DateOnly: Story = {
  args: {
    date: '2025-01-29T00:00:00.000Z',
  },
}
