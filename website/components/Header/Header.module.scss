.header {
  width: 100%;
  background-color: var(--white);
  position: sticky;
  top: 0;
  @include z-index('header');

  @include bp(tablet) {
    position: relative;
    padding: 0 var(--page-gutter);
  }
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
}

.desktopHeader {
  display: none;

  @include bp(tablet) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: px(16) 0;
  }
}

.logo {
  flex-shrink: 0;
}

.logo__img {
  width: px(259);
}

.logo__text {
  @include font-heading-sm;
  font-weight: 700;
}

.searchBar {
  display: none;
  flex-grow: 1;
  max-width: px(691);
  margin: 0 px(16);

  @include bp(tablet) {
    display: block;
  }
}

.authButtons {
  display: flex;
  align-items: center;
  gap: px(8);
}

.navigation {
  display: none;
  position: relative;

  @include bp(tablet) {
    display: block;
  }
}

.nav {
  display: flex;
  align-items: center;
  padding: px(8) 0;
  gap: px(4);
  border-bottom: 1px solid rgba($navy-dark, 0.2);
}

.nav__item {
  position: relative;
  display: flex;
  cursor: pointer;

  @include hover {
    &:not(.hover-disabled) .submenu {
      display: flex;
    }
  }

  // align first few submenu items to left edge of nav item as to not overflow
  &:nth-child(-n + 2) {
    .submenu {
      left: 0;
    }
  }

  // align last few submenu items to right edge of nav item as to not overflow
  &:nth-last-child(-n + 2) {
    .submenu {
      right: 0;
      left: auto;
    }
  }

  &.hover-disabled {
    .submenu {
      display: none;
    }
  }
}

.nav__link {
  @include font-text-base;
  font-weight: 700;
  color: var(--navy-dark);
  padding: px(8) px(12);

  &.active {
    background-color: var(--highlighter);
  }
}

.nav__button {
  color: var(--slate);
  font-weight: 500;

  @include hover {
    color: var(--slate-dark);
  }

  &.highlighted {
    color: var(--loud-warm);
    font-weight: 700;
  }
}

.submenu {
  @include z-index('submenu');
  display: none;
  position: absolute;
  top: 70%;
  left: 100%;
  margin-top: px(4);
  width: max-content;
  min-width: px(200);
  max-width: calc(100vw - px(80));
  background-color: var(--white);
  box-shadow: 0 px(10) px(15) rgba(0, 0, 0, 0.1);
  padding: px(25);
  border: 1px solid rgba($navy-dark, 0.2);
  transform: translateX(-50%);
}

.submenu__group {
  margin-bottom: px(16);
  margin-right: px(24);

  &:last-child {
    margin-bottom: 0;
    margin-right: 0;
  }
}

.submenu__title {
  @include font-text-base;
  font-weight: 700;
  color: var(--navy-dark);
}

.submenu__list {
  @include reset-ul;
}

.submenu__header_afterLink {
  margin-top: px(24);
}

.submenu__link {
  @include font-text-sm;
  color: var(--navy-dark);
}

.bowtieContainer {
  position: relative;
  display: flex;
  justify-content: center;
  height: px(36);
}

.bowtie {
  position: absolute;
  @include z-index('bowtie');
  top: px(-24);
  right: 22.5%;
  background-color: var(--white);
  padding: 0 px(8);
}

.mobileHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: px(12) px(16);
  margin: 0 auto;
  min-height: var(--header-height);
  border-bottom: 1px solid rgba($navy-dark, 0.2);
  height: var(--header-height);
  background-color: var(--white);
  @include z-index('mobile-header');

  @include bp(tablet) {
    display: none;
  }
}

.mobileLogo {
  display: flex;
  align-items: center;

  span {
    @include font-text-base;
    font-weight: 700;
    color: var(--navy-dark);
  }
}

.mobileLogo__img {
  width: px(150);
  height: auto;
}

.mobileLogo__text {
  @include font-heading-sm;
  color: var(--navy-dark);
}

.mobileNav {
  display: flex;
  align-items: center;
  gap: px(16);
}

.mobileNav__button {
  @include reset-button;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--navy-dark);
}

.mobileNav__icon {
  width: px(24);
}

.mobileNav__openButton {
  .mobileHeader[data-menu-open='true'] & {
    display: none;
  }
}

.mobileNav__closeButton {
  display: none;

  .mobileHeader[data-menu-open='true'] & {
    display: flex;
  }
}

.mobileHeader[data-search-open='true'] {
  .mobileLogo,
  .mobileNav__button {
    visibility: hidden;
  }
}

.mobileSearchOverlay {
  @include position-100(absolute);
  height: 100vh;
  background-color: var(--white);
  @include z-index('submenu');
  display: flex;
  flex-direction: column;

  @include bp(tablet) {
    display: none;
  }
}

.mobileSearchOverlay__header {
  padding: px(12) px(16);
  height: px(48);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid rgba($navy-dark, 0.1);
}

.mobileSearchOverlay__close {
  @include reset-button;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: px(4);
}

.mobileSearchOverlay__closeIcon {
  width: px(24);
  color: var(--navy-dark);
}

.mobileSearchOverlay__searchContainer {
  padding: px(16);
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba($navy-dark, 0.1);
}

.mobileMenuOverlay {
  position: fixed;
  top: var(--header-height);
  left: 0;
  width: 100%;
  background-color: var(--white);
  @include z-index('mobile-menu-overlay');
  display: flex;
  flex-direction: column;
  height: 100vh;

  @include bp(tablet) {
    display: none;
  }
}

.mobileMenu {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.mobileMenu__logo {
  display: flex;
  align-items: center;
}

.mobileMenu__logoImg {
  width: px(150);
  height: auto;
}

.mobileMenu__logoText {
  @include font-heading-sm;
  color: var(--navy-dark);
}

.mobileMenu__closeButton {
  @include reset-button;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobileMenu__closeIcon {
  width: px(24);
  height: px(24);
  color: var(--navy-dark);
}

.mobileMenu__nav {
  flex: 1;
  overflow-y: auto;
  padding: px(16);
}

.mobileMenu__list {
  @include reset-ul;
}

.mobileMenu__item {
  border-bottom: 1px solid rgba($navy-dark, 0.2);
}

.mobileMenu__link,
.mobileMenu__button {
  @include reset-button;
  @include font-heading-md;
  width: 100%;
  padding: px(12);
  color: var(--navy-dark);
  text-align: left;
  position: relative;

  &.active {
    background-color: var(--highlighter);
  }
}

.mobileMenu__button {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobileMenu__buttonIcon {
  width: px(24);
  color: var(--navy-dark);
}

.mobileMenu__footer {
  padding: px(16) 0;
  margin-top: auto;
}

.mobileMenu__buttons {
  display: flex;
  gap: px(16);
}

.mobileMenu__loginButton,
.mobileMenu__signupButton {
  flex: 1;
}

.mobileSubmenu {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: px(12) px(16);
}

.mobileSubmenu__backButton {
  @include reset-button;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobileSubmenu__backIcon {
  width: px(24);
  color: var(--navy-dark);
}

.mobileSubmenu__content {
  flex: 1;
  overflow-y: auto;
}

.mobileSubmenu__groups {
  display: flex;
  flex-direction: column;
}

.mobileSubmenu__group {
  border-bottom: 1px solid rgba($navy-dark, 0.2);
  padding: px(16) 0;
}

.mobileSubmenu__list {
  @include reset-ul;
}

.mobileSubmenu__item {
  margin-bottom: px(16);

  &:last-child {
    margin-bottom: 0;
  }
}

.mobileSubmenu__headerText {
  @include font-text-base;
  color: var(--navy-dark);
  font-weight: 700;
}

.mobileSubmenu__headerLink {
  color: var(--navy-dark);
}

.mobileSubmenu__link {
  @include font-text-base;
  color: var(--navy-dark);
  display: flex;
  align-items: center;
  position: relative;
  @include z-index('main');
}
