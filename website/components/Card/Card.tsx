'use client'

import classnames from 'classnames'
import styles from './Card.module.scss'
import SanityImage from '@/components/SanityImage/SanityImage'
import { CardProps } from '@/types/components/CardProps'
import Link from '../Link/Link'
import { CSSProperties, useMemo, useRef, useState } from 'react'
import Button from '../Button/Button'
import { getDateParts } from '@/lib/utils/date'
import { ImageColumns } from '@/types/sanity/SanityImage'
import useBreakpoint from '@/hooks/use-breakpoint'
import Tag from '../Tag/Tag'
import EpisodeAndDate from '../EpisodeAndDate/EpisodeAndDate'
import StarsRating from '../StarsRating/StarsRating'
import PriceDisplay from '../PriceDisplay/PriceDisplay'
import AuthorDateDisplay from '../AuthorDateDisplay/AuthorDateDisplay'

const Card = ({
  className,
  title,
  description,
  image,
  link,
  titleSize,
  capLines = 2,
  orientation = 'portrait',
  imageSide = 'right',
  padding = true,
  imageOrientation = '4:3',
  tagProps,
  price,
  author,
  date,
  additionalText,
  showDateSquare,
  allowFavoriting,
  imageColumns = 2,
  arrowButtonText,
  episodeAndDateProps,
  starsRatingProps,
}: CardProps) => {
  const [isHover, setIsHover] = useState(false)
  const { isMobile } = useBreakpoint()
  const $arrowButton = useRef<ButtonImperativeHandle | null>(null)
  const cappedLinesNumber = useMemo(() => {
    if (capLines === false) return 0
    if (capLines === true) return 2
    return capLines
  }, [capLines])
  const tagSizeCalculated = useMemo(() => {
    if (tagProps?.size) return tagProps?.size
    if (imageOrientation === 'square-xs') {
      return 'sm'
    }
    return 'md'
  }, [tagProps?.size, imageOrientation])
  const titleSizeCalculated = useMemo(() => {
    if (titleSize) return titleSize
    if (orientation === 'portrait') return 'md'
    if (imageOrientation === 'square-xs' || imageOrientation === 'square-sm') {
      return 'sm'
    }
    return 'md'
  }, [imageOrientation, orientation, titleSize])

  if (!title) return null

  return (
    <article
      className={classnames(
        styles.Card,
        className,
        {
          [styles.hasPadding]: padding,
        },
        {
          [styles.showDateSquare]: showDateSquare,
        },
        {
          [styles.capLines]: capLines,
        },
        {
          [styles.isHover]: isHover,
        },
      )}
      data-card-orientation={orientation}
      data-card-image-side={imageSide}
      data-card-image-orientation={imageOrientation}
      data-heading-size={titleSizeCalculated}
      style={{ '--capped-lines': cappedLinesNumber } as CSSProperties}
      onMouseOver={() => {
        if (isMobile || !link) return
        setIsHover(true)
      }}
      onMouseLeave={() => {
        if (isMobile || !link) return
        setIsHover(false)
      }}
    >
      <div className={styles.cardInner}>
        <div className={styles.imageWrapper}>
          <ContentContainer
            className={styles.imageWrapperAspectRatio}
            link={link}
          >
            {image && (
              <SanityImage
                source={image}
                className={styles.image}
                isCover
                columns={imageColumns as ImageColumns}
                animated={false}
              />
            )}
          </ContentContainer>
          {allowFavoriting && imageOrientation !== 'square-xs' && (
            <Button
              onClick={() => {
                // TODO: Add functionality into clicking button
                // eslint-disable-next-line
                console.log('Favourited Item')
              }}
              icon="bookmark"
              className={styles.bookmarkButton}
            />
          )}
          {showDateSquare && date && (
            <p className={styles.dateSquare}>
              <span className={styles.dateSquare__month}>{getDateParts(date).month}</span>
              <span className={styles.dateSquare__day}>{getDateParts(date).day}</span>
            </p>
          )}
        </div>
        <ContentContainer
          className={styles.content}
          link={link}
        >
          <div className={styles.contentInner}>
            {tagProps && (
              <Tag
                text={tagProps?.text}
                size={tagSizeCalculated}
                className={styles.tag}
              />
            )}
            {episodeAndDateProps && (
              <EpisodeAndDate
                {...episodeAndDateProps}
                className={styles.episodeTitle}
              />
            )}
            <h3 className={styles.title}>{title}</h3>
            {description && <p className={styles.description}>{description}</p>}
            {additionalText && <p className={styles.additionalText}>{additionalText}</p>}
            {author && (
              <AuthorDateDisplay
                className={styles.authorAndDate}
                authors={[author]}
                date={date}
              />
            )}
            {price?.default && (
              <PriceDisplay
                price={price.default}
                discountPrice={price.memberPrice}
                showMemberDiscountText={true}
                className={styles.price}
              />
            )}
            {arrowButtonText && link && (
              <Button
                label={arrowButtonText}
                className={styles.arrowButton}
                icon="arrowRight"
                style="bare"
                bareColor="loud"
                iconPosition="right"
                disableHoverAnimation={true}
                ref={$arrowButton}
              />
            )}
            {starsRatingProps && (
              <StarsRating
                className={styles.starsRating}
                size="sm"
                {...starsRatingProps}
              />
            )}
          </div>
        </ContentContainer>
      </div>
    </article>
  )
}

Card.displayName = 'Card'

const ContentContainer = ({
  children,
  link,
  className,
}: {
  children: React.ReactNode
  link?: SanityLink
  className?: string
}) => {
  if (link && link.linkType !== 'disabled') {
    return (
      <Link
        link={link}
        className={classnames(className)}
      >
        {children}
      </Link>
    )
  }

  return <div className={classnames(className)}>{children}</div>
}

export default Card
