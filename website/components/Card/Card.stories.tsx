import type { Meta, StoryObj } from '@storybook/react'
import Card from './Card'
import { SANITY_IMAGE_STUB_SOURCE } from '../SanityImage/SanityImage.stub'
import styles from './Card.module.scss'
import { STUB_LINK } from '../Link/Link.stub'
import { CARD_BASIC_PROPS } from './Card.stub'

const PortraitContainer = ({ children }: { children: React.ReactNode }) => (
  <div className={styles.storybookPortraitContainer}>{children}</div>
)
const LandscapeContainer = ({ children }: { children: React.ReactNode }) => (
  <div className={styles.storybookLandscapeContainer}>{children}</div>
)

const meta = {
  title: 'Cards/Card',
  component: Card,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    orientation: {
      options: ['portrait', 'landscape'],
      control: { type: 'radio' },
    },
    imageOrientation: {
      options: ['square', '4:3', '16:9', 'magazine', 'square-xs', 'square-sm', 'square-md', 'square-lg'],
      control: { type: 'select' },
    },
    capLines: {
      options: [true, false, 1, 2, 3, 4, 5],
      control: { type: 'select' },
    },
    imageSide: {
      options: ['left', 'right'],
      control: { type: 'radio' },
    },
    padding: {
      options: [true, false],
      control: { type: 'radio' },
    },
  },
} satisfies Meta<typeof Card>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    orientation: 'portrait',
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const Landscape: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    orientation: 'landscape',
  },
  render: args => (
    <LandscapeContainer>
      <Card {...args} />
    </LandscapeContainer>
  ),
}

export const NoPadding: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    orientation: 'landscape',
    padding: false,
  },
  render: args => (
    <LandscapeContainer>
      <Card {...args} />
    </LandscapeContainer>
  ),
}

export const LandscapeCappedDescription: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla facilisi. Nulla. facilisi. Nulla facilisi. Nulla facilisi. Nulla.',
    orientation: 'landscape',
    capLines: 2,
  },
  render: args => (
    <LandscapeContainer>
      <Card {...args} />
    </LandscapeContainer>
  ),
}

export const ContentOnLeft: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    orientation: 'landscape',
    imageSide: 'right',
  },
  render: args => (
    <LandscapeContainer>
      <Card {...args} />
    </LandscapeContainer>
  ),
}

export const WithLink: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    link: STUB_LINK.TEST_WITH_LABEL,
  },
  render: args => (
    <LandscapeContainer>
      <Card {...args} />
    </LandscapeContainer>
  ),
}

export const WithBookmarkButton: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    allowFavoriting: true,
    link: STUB_LINK.TEST_WITH_LABEL,
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const WithAdditionalText: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    additionalText: 'Wednesday, Jan. 29 @ 6 pm EST',
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const WithAuthorAndDate: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    author: 'Lemmerd Spanks',
    date: '2024-01-24T15:30:00.000Z',
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const WithTag: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    tagProps: {
      text: 'Recipe',
    },
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const WithStarsRating: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    starsRatingProps: {
      percent: 0.8,
      count: 12,
    },
  },
  render: args => (
    <LandscapeContainer>
      <Card {...args} />
    </LandscapeContainer>
  ),
}

export const DateSquare: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    date: '2024-01-24T15:30:00.000Z',
    showDateSquare: true,
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const LargerImageSize: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    imageColumns: 5,
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const WithPrice: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    price: {
      default: 10.2,
      memberPrice: 5,
    },
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const WithEpisodeTitle: Story = {
  args: {
    ...CARD_BASIC_PROPS,
    episodeAndDateProps: {
      season: 8,
      episode: 821,
      date: '2025-01-29T00:00:00.000Z',
    },
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const ProductExample: Story = {
  args: {
    title: 'Store Item Name',
    description: 'If you really want to taste the soul of Thai cooking, it’s worth trying a little harder.',
    image: SANITY_IMAGE_STUB_SOURCE.PRODUCT,
    imageOrientation: 'square',
    price: {
      default: 119.95,
      memberPrice: 99.95,
    },
    padding: false,
    starsRatingProps: {
      percent: 0.8,
      count: 12,
    },
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const RecipeExample: Story = {
  args: {
    title: 'Slow-Cooked Short Rib Ragù',
    description: 'Recipe description here Lorem ipsum dolor sit amet, consectetur.',
    image: SANITY_IMAGE_STUB_SOURCE.FOOD,
    imageOrientation: 'square',
    allowFavoriting: true,
    tagProps: {
      text: 'Recipe',
    },
    starsRatingProps: {
      percent: 0.8,
      count: 12,
    },
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const BlogExample: Story = {
  args: {
    title: 'Eat, Drink, Read: Dwight Garners Obsession with Word and Table',
    image: SANITY_IMAGE_STUB_SOURCE.ARTICLE,
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const EpisodeExample: Story = {
  args: {
    title: 'New Bistro Classics',
    image: SANITY_IMAGE_STUB_SOURCE.ARTICLE,
    description: 'Recipe dek here Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
    episodeAndDateProps: {
      season: 8,
      episode: 821,
      date: '2025-01-29T00:00:00.000Z',
    },
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}

export const MagazineExample: Story = {
  args: {
    title: 'June - July 2025',
    image: SANITY_IMAGE_STUB_SOURCE.MAGAZINE,
    imageOrientation: 'magazine',
    arrowButtonText: 'Link text here', // needs link component
    link: STUB_LINK.TEST_WITH_LABEL,
    orientation: 'landscape',
    padding: false,
  },
  render: args => (
    <PortraitContainer>
      <Card {...args} />
    </PortraitContainer>
  ),
}
