'use client'

import classnames from 'classnames'
import styles from './EyebrowAndTitleSection.module.scss'
import RichTextV2 from '@/components/RichTextV2/RichTextV2'

const EyebrowAndTitleSection = ({ className, eyebrow, title, description }: SanityEyebrowAndTitleSection) => {
  if (!title) return null

  return (
    <div className={classnames(styles.EyebrowAndTitleSection, className)}>
      <div
        className={styles.inner}
        role="note"
        aria-label={title}
      >
        {eyebrow && (
          <div className={styles.headerRow}>
            <hr
              className={styles.divider}
              aria-hidden="true"
            />
            <span className={styles.eyebrow}>{eyebrow}</span>
            <hr
              className={styles.divider}
              aria-hidden="true"
            />
          </div>
        )}
        <div className={styles.title}>{title}</div>
        {description && description.length > 0 && (
          <div className={styles.description}>
            <RichTextV2 content={description} />
          </div>
        )}
      </div>
    </div>
  )
}

EyebrowAndTitleSection.displayName = 'EyebrowAndTitleSection'

export default EyebrowAndTitleSection
