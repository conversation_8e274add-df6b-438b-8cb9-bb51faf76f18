.RichTextSection {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);

  @include bp(tablet, true) {
    display: flex !important;
    flex-direction: column !important;
  }

  [data-rich-text-section-has-sidebar='true'] & {
    display: grid;
    grid-template-columns: minmax(0, 0.33fr) minmax(0, 0.67fr);
    gap: px(42);

    @include bp(laptop) {
      gap: px(80);
    }
  }

  [data-rich-text-section-sidebar-side='right'] & {
    @include bp(tablet) {
      grid-template-columns: minmax(0, 0.67fr) minmax(0, 0.33fr);
    }
  }
}

.richText {
  @include rich-text-base-styling;
  @include font-text-lg;

  [data-rich-text-section-text-alignment='center'] & {
    text-align: center;
  }

  [data-rich-text-section-content-width='md'] & {
    max-width: px(700);
    margin: 0 auto;
  }

  [data-h2] {
    @include font-heading-lg;
    margin: 1.4em 0 0.9em;
  }

  [data-h3] {
    @include font-heading-md;
    margin: 1.4em 0 0.9em;
  }

  @include bp(tablet) {
    flex: 2;
  }
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: px(24);

  @include bp(tablet) {
    flex: 1;
  }

  [data-rich-text-section-sidebar-side='right'] & {
    @include bp(tablet) {
      order: 2;
    }
  }
}

.sidebarCardItems {
  display: flex;
  flex-direction: column;
  gap: px(24);
}

.sidebarCard {
  width: 100%;
}

.richTextTip {
  margin: px(32) 0;
}
