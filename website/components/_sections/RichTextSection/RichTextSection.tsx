'use client'

import classnames from 'classnames'
import styles from './RichTextSection.module.scss'
import RichTextV2, { portableTextSerializer } from '@/components/RichTextV2/RichTextV2'
import Sidebar from '@/components/_sections/Sidebar/Sidebar'
import { PortableTextReactComponents } from 'next-sanity'
import TipComponent from '@/components/Tip/Tip'

const RichTextSection = ({
  className,
  content,
  hasSidebar,
  sidebarSide = 'left',
  sidebarContent,
  textAlignment = 'left',
  contentWidth = 'full',
}: SanityRichTextSection) => {
  if (!content) return null

  const renderSidebarContent = () => {
    if (!sidebarContent?.length) return null

    return sidebarContent.map((item, index) => {
      if (item._type === 'sidebar') {
        return (
          <Sidebar
            key={index}
            {...(item as SanitySidebar)}
          />
        )
      }
      return null
    })
  }

  return (
    <div
      data-rich-text-section-has-sidebar={hasSidebar}
      data-rich-text-section-sidebar-side={sidebarSide}
      data-rich-text-section-text-alignment={hasSidebar ? 'left' : textAlignment}
      data-rich-text-section-content-width={hasSidebar ? 'full' : contentWidth}
      className={classnames(styles.RichTextSection, className)}
    >
      <div className={styles.inner}>
        {hasSidebar && <div className={styles.sidebar}>{renderSidebarContent()}</div>}
        <div className={styles.richText}>
          <RichTextV2
            content={content}
            serializer={RICH_TEXT_SERIALIZER}
          />
        </div>
      </div>
    </div>
  )
}

const RICH_TEXT_SERIALIZER: Partial<PortableTextReactComponents> = {
  ...portableTextSerializer,
  types: {
    ...portableTextSerializer.types,
    tip: ({ value }: { value: { content: TipProps } }) => {
      if (!value?.content) return null
      return (
        <TipComponent
          className={styles.richTextTip}
          {...value?.content}
        />
      )
    },
  },
}

RichTextSection.displayName = 'RichTextSection'

export default RichTextSection
