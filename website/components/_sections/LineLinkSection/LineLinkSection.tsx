import React from 'react'
import Link from '@/components/Link/Link'
import styles from './LineLinkSection.module.scss'
import classnames from 'classnames'

const LineLinkSection = ({ link, className }: SanityLineLinkSection) => {
  if (!link) return null

  return (
    <div className={classnames(styles.lineLinkSection, className)}>
      <div className={styles.inner}>
        <hr
          className={styles.divider}
          aria-hidden="true"
        />
        <Link
          className={styles.link}
          link={link}
        />
        <hr
          className={styles.divider}
          aria-hidden="true"
        />
      </div>
    </div>
  )
}

export default LineLinkSection
