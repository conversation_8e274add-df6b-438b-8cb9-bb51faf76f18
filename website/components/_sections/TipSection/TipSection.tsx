'use client'

import classnames from 'classnames'
import styles from './TipSection.module.scss'
import TipComponent from '@/components/Tip/Tip'

const TipSection = ({ className, content }: SanityTipSection) => {
  if (!content?.title || !content?.description) return null

  return (
    <div className={classnames(styles.TipSection, className)}>
      <div className={styles.inner}>
        <TipComponent {...content} />
      </div>
    </div>
  )
}

TipSection.displayName = 'TipSection'

export default TipSection
