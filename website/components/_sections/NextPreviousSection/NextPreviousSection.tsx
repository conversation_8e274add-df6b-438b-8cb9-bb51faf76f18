'use client'

import { useMemo } from 'react'
import classnames from 'classnames'
import styles from './NextPreviousSection.module.scss'
import NextPrevious from '@/components/NextPrevious/NextPrevious'
import { formatCardItemObjectToCardProps } from '@/lib/utils/card'
import { DOC_TYPES } from '@/data'

const NextPreviousSection = ({ className, items, labelType }: NextPreviousSectionProps) => {
  const formattedItems = useMemo(() => {
    if (!items || items.length === 0) return []

    return items.map(item => formatCardItemObjectToCardProps(item))
  }, [items])

  if (!formattedItems || formattedItems.length < 2) return null

  return (
    <div className={classnames(styles.NextPreviousSection, className)}>
      <div className={styles.inner}>
        {formattedItems[0] && (
          <div className={classnames(styles.item, styles.previous)}>
            <NextPrevious
              direction="previous"
              title={formattedItems[0].title}
              image={formattedItems[0].image}
              link={formattedItems[0].link}
              episodeTitle={
                formattedItems[0]?._type === DOC_TYPES.TV_EPISODE &&
                formattedItems[0]?.episodeAndDateProps?.season &&
                formattedItems[0]?.episodeAndDateProps?.episode && {
                  season: formattedItems[0]?.episodeAndDateProps?.season,
                  episode: formattedItems[0]?.episodeAndDateProps?.episode,
                  date: formattedItems[0]?.episodeAndDateProps?.date
                    ? formattedItems[0]?.episodeAndDateProps?.date
                    : null,
                }
              }
              labelType={labelType}
            />
          </div>
        )}
        {formattedItems[1] && (
          <div className={classnames(styles.item, styles.next)}>
            <NextPrevious
              direction="next"
              title={formattedItems[1].title}
              image={formattedItems[1].image}
              link={formattedItems[1].link}
              episodeTitle={
                formattedItems[1]?._type === DOC_TYPES.TV_EPISODE &&
                formattedItems[1]?.episodeAndDateProps?.season &&
                formattedItems[1]?.episodeAndDateProps?.episode && {
                  season: formattedItems[1]?.episodeAndDateProps?.season,
                  episode: formattedItems[1]?.episodeAndDateProps?.episode,
                  date: formattedItems[1]?.episodeAndDateProps?.date
                    ? formattedItems[1]?.episodeAndDateProps?.date
                    : null,
                }
              }
              labelType={labelType}
            />
          </div>
        )}
      </div>
    </div>
  )
}

NextPreviousSection.displayName = 'NextPreviousSection'

export default NextPreviousSection
