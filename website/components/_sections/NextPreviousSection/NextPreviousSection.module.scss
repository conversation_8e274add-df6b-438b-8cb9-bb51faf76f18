.NextPreviousSection {
  padding: 0 var(--page-gutter);
  width: 100%;
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
  display: flex;
  flex-direction: column;

  @include bp(tablet) {
    flex-direction: row;
    justify-content: space-between;
  }
}

.item {
  margin-bottom: px(24);

  &:last-child {
    margin-bottom: 0;
  }

  @include bp(tablet) {
    margin-bottom: 0;
  }
}

.previous {
  align-self: flex-start;
}

.next {
  align-self: flex-end;
}
