.TextCarouselWithLogos {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
}

.carousel {
  margin-bottom: px(48);
}

.logoContainer {
  display: flex;
  flex-flow: wrap;
  gap: px(40);
  justify-content: center;
  align-items: center;
}

.logoItem {
  width: var(--mobile-width, #{px(100)});

  @include bp(tablet) {
    width: var(--desktop-width, #{px(100)});
  }
}

.logo {
  width: 100%;
  object-fit: contain;
}
