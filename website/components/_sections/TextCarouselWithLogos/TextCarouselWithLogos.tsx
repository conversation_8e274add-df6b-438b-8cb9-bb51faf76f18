import React from 'react'
import SanityImage from '@/components/SanityImage/SanityImage'
import TextCarousel from '@/components/TextCarousel/TextCarousel'
import styles from './TextCarouselWithLogos.module.scss'

export const TextCarouselWithLogos = ({ carouselItems, logoItems }: SanityTextCarouselWithLogos) => {
  return (
    <div className={styles.TextCarouselWithLogos}>
      <div className={styles.inner}>
        <TextCarousel
          items={carouselItems.map((item: { title: string; subtitle: string }) => ({
            description: item.title,
            subtitle: item.subtitle,
          }))}
          className={styles.carousel}
        />

        <div className={styles.logoContainer}>
          {logoItems?.map((logo: SanityTextCarouselWithLogosLogoItem, index: number) => (
            <div
              key={`logo-${index}`}
              className={styles.logoItem}
              style={
                {
                  '--desktop-width': logo.desktopWidth ? `${logo.desktopWidth}px` : '100px',
                  '--mobile-width': logo.mobileWidth ? `${logo.mobileWidth}px` : '100px',
                } as React.CSSProperties
              }
            >
              <SanityImage
                source={logo.image}
                className={styles.logo}
                columns={{
                  md: 3, // safe amount
                  sm: 6, // safe amount
                }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default TextCarouselWithLogos
