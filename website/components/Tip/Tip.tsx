'use client'

import React from 'react'
import classnames from 'classnames'
import RichTextV2 from '../RichTextV2/RichTextV2'
import styles from './Tip.module.scss'

const Tip = ({
  title,
  description,
  alignment = 'left',
  backgroundColor = 'white',
  textSize = 'md',
  className,
}: TipProps) => {
  const isRichText = Array.isArray(description)

  if (!title || !description?.length) return null

  return (
    <div
      className={classnames(styles.Tip, className)}
      data-tip-alignment={alignment}
      data-tip-background-color={backgroundColor}
      data-tip-text-size={textSize}
      role="note"
      aria-label={title}
    >
      <div className={styles.headerRow}>
        <hr
          className={styles.divider}
          aria-hidden="true"
        />
        <span className={styles.title}>{title}</span>
        <hr
          className={styles.divider}
          aria-hidden="true"
        />
      </div>
      <div className={styles.description}>
        {isRichText ? <RichTextV2 content={description} /> : <p data-p>{description}</p>}
      </div>
    </div>
  )
}

Tip.displayName = 'Tip'

export default Tip
